<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class CorsFilter implements FilterInterface
{
    /**
     * Do whatever processing this filter needs to do.
     * By default it should not return anything during
     * normal execution. However, when an abnormal state
     * is found, it should return an instance of
     * CodeIgniter\HTTP\Response. If it does, script
     * execution will end and that Response will be
     * sent back to the client, allowing for error pages,
     * redirects, etc.
     *
     * @param RequestInterface $request
     * @param array|null       $arguments
     *
     * @return RequestInterface|ResponseInterface|string|void
     */
    public function before(RequestInterface $request, $arguments = null)
    {
        $response = service('response');

        // Allow all origins or specify specific ones
        $allowedOrigins = ['http://localhost:3000'];  // Frontend React app origin

        $response->setHeader('Access-Control-Allow-Origin', $allowedOrigins);

        // Allow specific methods (GET, POST, PUT, DELETE, OPTIONS)
        $response->setHeader('Access-Control-Allow-Methods', 'GET, POST, PATCH, PUT, DELETE, OPTIONS');
        // Allow specific headers
        $response->setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
        // Allow credentials (cookies, headers)
        $response->setHeader('Access-Control-Allow-Credentials', 'true');

        // Handle OPTIONS preflight requests
        if ($request->getMethod() === 'options') {
            $response->setHeader('Access-Control-Max-Age', 86400);  // Cache preflight response for 24 hours
            return $response;  // End the request here for OPTIONS preflight requests
        }

        return null;  // Continue with the request if not OPTIONS
    }

    /**
     * Allows After filters to inspect and modify the response
     * object as needed. This method does not allow any way
     * to stop execution of other after filters, short of
     * throwing an Exception or Error.
     *
     * @param RequestInterface  $request
     * @param ResponseInterface $response
     * @param array|null        $arguments
     *
     * @return ResponseInterface|void
     */
    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        //
    }
}
